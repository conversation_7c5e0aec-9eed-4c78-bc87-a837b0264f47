<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tekla构件编号筛选插件 - 突破长度限制</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #34495e;
        }
        textarea {
            width: 100%;
            height: 150px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            resize: vertical;
            box-sizing: border-box;
        }
        textarea:focus {
            border-color: #3498db;
            outline: none;
        }
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input[type="text"]:focus {
            border-color: #3498db;
            outline: none;
        }
        .button-group {
            text-align: center;
            margin: 30px 0;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .success {
            background-color: #d5f4e6;
            border-left-color: #27ae60;
            color: #27ae60;
        }
        .error {
            background-color: #ffeaa7;
            border-left-color: #e17055;
            color: #e17055;
        }
        .instructions {
            background-color: #e8f4fd;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        .instructions h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
        .file-preview {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Tekla构件编号筛选插件</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>在下方文本框中粘贴或输入构件编号（支持多个编号，用逗号、空格或换行分隔）</li>
                <li>设置文件名（可选，默认为"突破长度限制-构件位置编号"）</li>
                <li>点击"生成VObjGrp文件"按钮</li>
                <li>下载生成的文件并放置到Tekla模型的attributes文件夹内</li>
                <li>在Tekla软件筛选界面选中新建的文件名即可突破文本长度限制</li>
            </ol>
        </div>

        <div class="input-group">
            <label for="componentNumbers">构件编号（支持多个编号）：</label>
            <textarea id="componentNumbers" placeholder="请输入构件编号，支持多种格式：&#10;GL-1 GL-2 GL-3（空格分隔）&#10;GL-1,GL-2,GL-3（逗号分隔）&#10;或者每行一个编号：&#10;GL-1&#10;GL-2&#10;GL-3" oninput="previewCleanedNumbers()"></textarea>
            <div id="cleanedPreview" style="display:none; margin-top:10px; padding:10px; background-color:#f8f9fa; border-radius:3px; font-size:12px; color:#666;">
                <strong>清理后的编号：</strong><span id="cleanedText"></span>
            </div>
        </div>

        <div class="input-group">
            <label for="fileName">文件名（不含扩展名）：</label>
            <input type="text" id="fileName" value="突破长度限制-构件位置编号" placeholder="输入文件名">
        </div>

        <div class="button-group">
            <button onclick="generateFile()">🚀 生成VObjGrp文件</button>
            <button onclick="clearAll()">🗑️ 清空内容</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        function generateFile() {
            const componentNumbers = document.getElementById('componentNumbers').value.trim();
            const fileName = document.getElementById('fileName').value.trim() || '突破长度限制-构件位置编号';
            const resultDiv = document.getElementById('result');

            if (!componentNumbers) {
                showResult('请输入构件编号！', 'error');
                return;
            }

            try {
                // 解析构件编号
                const numbers = parseComponentNumbers(componentNumbers);
                
                if (numbers.length === 0) {
                    showResult('未找到有效的构件编号！', 'error');
                    return;
                }

                // 生成文件内容
                const fileContent = generateVObjGrpContent(numbers);
                
                // 创建并下载文件
                downloadFile(fileContent, fileName + '.VObjGrp');
                
                showResult(`✅ 成功生成文件！包含 ${numbers.length} 个构件编号。<br>文件名：${fileName}.VObjGrp`, 'success', fileContent);
                
            } catch (error) {
                showResult('生成文件时出错：' + error.message, 'error');
            }
        }

        function parseComponentNumbers(input) {
            // 支持多种分隔符：逗号、分号、空格、换行、STX字符
            const stxChar = String.fromCharCode(2); // STX控制字符
            const numbers = input
                .split(new RegExp(`[,;\\s\\n\\r${stxChar}]+`))
                .map(num => num.trim())
                .filter(num => num.length > 0);

            return [...new Set(numbers)]; // 去重
        }

        function generateVObjGrpContent(numbers) {
            // 将所有构件编号用STX字符(ASCII 0x02)连接，这样Tekla才能正确识别
            const stxChar = String.fromCharCode(2); // STX控制字符
            const numbersString = numbers.join(stxChar);

            let content = `TITLE_OBJECT_GROUP
{
    Version= 1.05
    Count= 1
    SECTION_OBJECT_GROUP
    {
        0
        1
        co_assembly
        proNAME
        albl_Name
        ==
        albl_Equals
        ${numbersString}
        0
        &&
        }
    }
`;
            return content;
        }

        function downloadFile(content, filename) {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
        }

        function showResult(message, type, fileContent = null) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
            
            if (fileContent) {
                resultDiv.innerHTML += `<div class="file-preview">${fileContent}</div>`;
            }
        }

        function clearAll() {
            document.getElementById('componentNumbers').value = '';
            document.getElementById('fileName').value = '突破长度限制-构件位置编号';
            document.getElementById('result').innerHTML = '';
        }

        // 示例数据
        window.onload = function() {
            document.getElementById('componentNumbers').value = 'GL-1 GL-2 GL-3 GL-4 GL-5';
        };
    </script>
</body>
</html>
